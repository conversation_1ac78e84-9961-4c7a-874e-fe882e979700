<?xml version="1.0" encoding="UTF-8"?>
<Entities
 build="b68"
 majorVersion="8"
 minorVersion="3"
 modelPersistenceProviderPackage="PostgresPersistenceProviderPackage"
 revision="9"
 schemaVersion="1032"
 universal="">
    <Things>
        <Thing
         description=""
         documentationContent=""
         effectiveThingPackage="ConfiguredThing"
         enabled="true"
         homeMashup=""
         identifier=""
         lastModifiedDate="2025-07-29T11:46:54.716+08:00"
         name="Thing.Fn.ElectricTest"
         projectName=""
         published="false"
         tags=""
         thingTemplate="GenericThing"
         valueStream="">
            <avatar></avatar>
            <DesignTimePermissions>
                <Create></Create>
                <Read></Read>
                <Update></Update>
                <Delete></Delete>
                <Metadata></Metadata>
            </DesignTimePermissions>
            <RunTimePermissions></RunTimePermissions>
            <VisibilityPermissions>
                <Visibility></Visibility>
            </VisibilityPermissions>
            <ConfigurationTableDefinitions></ConfigurationTableDefinitions>
            <ConfigurationTables></ConfigurationTables>
            <ThingShape>
                <PropertyDefinitions>
                    <PropertyDefinition
                     aspect.dataChangeType="VALUE"
                     aspect.defaultValue="F:\ThingworxDataCollect\09-测试中心质量表"
                     aspect.isPersistent="true"
                     baseType="STRING"
                     category=""
                     description="同步目录"
                     isLocalOnly="false"
                     name="syncDirectory"
                     ordinal="2"></PropertyDefinition>
                    <PropertyDefinition
                     aspect.dataChangeType="VALUE"
                     aspect.defaultValue="11#$C1#$全型号测试日志表"
                     aspect.isPersistent="true"
                     baseType="STRING"
                     category=""
                     description="测试日志文件名称"
                     isLocalOnly="false"
                     name="testFileName"
                     ordinal="3"></PropertyDefinition>
                </PropertyDefinitions>
                <ServiceDefinitions>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="创建测试文件夹 date 2024/3/27 15:37"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="CreateTestFile">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions></ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="创建现场临时处理单的测试文件 date 2024年11月6日10:34:57"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="CreateTestTemporaryFile">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions></ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="获取所有的电测试系统的型号 date 2024/4/1 19:15"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="GetAllTestModel">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions></ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="date 2024/4/1 10:34"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="GetMappingNodeCode">
                        <ResultType
                         baseType="STRING"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="STRING"
                             description=""
                             name="nodeCode"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="获取所有的现场临时处理单的分类 wanghq 2024年11月8日14:49:48"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="GetTempCategory">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions></ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="获取查询临时处理单的sql wanghq 2024年11月7日16:19:00"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="GetTempSql">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="JSON"
                             description=""
                             name="query"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="获取测试事件查询项 date 2024/4/18 16:11"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="GetTestEventSearchItem">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="STRING"
                             description=""
                             name="modelName"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="date 2024/4/11 10:35"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="GetTestEventSql">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="JSON"
                             description=""
                             name="query"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="初始化现场临时处理单表 wanghq 2024年11月7日15:13:34"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="InitTemporaryTable">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions></ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="手动同步 date 2024/3/29 15:00"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="ManualSync">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="NUMBER"
                             description=""
                             name="treeId"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="wanghq 2025年5月22日9:55:52"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="PostCreateFile">
                        <ResultType
                         baseType="NOTHING"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions></ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="获取导出excel的数据 wanghq 2024年11月4日17:57:59"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="QueryExportData">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="JSON"
                             description=""
                             name="query"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="按照时间每一天统计不同分类下的数量和占比 wanghq 2024年11月4日15:27:25"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="QueryStatisticsDate">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="JSON"
                             description=""
                             name="query"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="获取型号统计的数据 wanghq 2024年11月4日17:16:01"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="QueryStatisticsModel">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="JSON"
                             description=""
                             name="query"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="获取临时处理单的完成状态  wanghq 2025年5月22日9:55:52"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="QueryTempCompletionStatus">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="JSON"
                             description=""
                             name="query"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="获取现场临时处理单的型号统计数据 wanghq 2024年11月7日16:08:01"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="QueryTempStatisticsModel">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="JSON"
                             description=""
                             name="query"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="date 2024/4/11 10:40"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="QueryTestEvent">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="INTEGER"
                             description=""
                             name="limit"
                             ordinal="2"></FieldDefinition>
                            <FieldDefinition
                             baseType="INTEGER"
                             description=""
                             name="page"
                             ordinal="1"></FieldDefinition>
                            <FieldDefinition
                             baseType="JSON"
                             description=""
                             name="query"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="date 2024/3/28 10:53"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="SaveTestTable">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="STRING"
                             description=""
                             name="fileStr"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="同步全部 date 2024/3/29 15:00"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="SyncAll">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions></ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="更新全型号测试日志数据 date 2024/4/10 16:02"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="UpdateAllTestLog">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions></ParameterDefinitions>
                    </ServiceDefinition>
                    <ServiceDefinition
                     aspect.isAsync="false"
                     category=""
                     description="更新全型号测试日志数据 date 2024/4/10 16:02"
                     isAllowOverride="false"
                     isLocalOnly="false"
                     isOpen="false"
                     isPrivate="false"
                     name="UpdateTestLog">
                        <ResultType
                         baseType="JSON"
                         description=""
                         name="result"
                         ordinal="0"></ResultType>
                        <ParameterDefinitions>
                            <FieldDefinition
                             baseType="NUMBER"
                             description=""
                             name="arrFirstIndex"
                             ordinal="1"></FieldDefinition>
                            <FieldDefinition
                             baseType="STRING"
                             description=""
                             name="dataStr"
                             ordinal="0"></FieldDefinition>
                        </ParameterDefinitions>
                    </ServiceDefinition>
                </ServiceDefinitions>
                <EventDefinitions></EventDefinitions>
                <ServiceMappings></ServiceMappings>
                <ServiceImplementations>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="CreateTestFile">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    CreateTestFile
                                         * @description   创建测试文件夹 date 2024/3/27 15:37
                                         * @implementation    {Script}
                                         *
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                        
                                            //随机生成10位数的文件编号
                                            function generateFileId() {
                                                var fileId = "";
                                                for (var i = 0; i < 10; i++) {
                                                    fileId += Math.floor(Math.random() * 10);
                                                }
                                                return fileId;
                                            }
                                        
                                            //随机生成文件名称
                                            function generateFileName() {
                                                var characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"; // 定义字符集
                                                var fileName = "";
                                                for (var i = 0; i < 8; i++) { // 文件名长度为8位
                                                    fileName += characters.charAt(Math.floor(Math.random() * characters.length)); // 从字符集中随机选择一个字符
                                                }
                                                return fileName;
                                            }
                                        
                                            function generateIndex(arrLen) {
                                                var randomInt = Math.floor(Math.random() * arrLen);
                                                var arrIndex = randomInt === arrLen ? 0 : randomInt;
                                                return arrIndex;
                                            }
                                        
                                            function getRandomValue(arr) {
                                                return arr[generateIndex(arr.length)];
                                            }
                                        
                                        
                                            var statuss = ['00', '11', '00', 'AA'];
                                            var hasAttachments = [true, false];
                                            var models = ["D1_1","D1_3","D1_4"];
                                            var nodes = ['载荷装星', 'TEST1', 'TEST2', 'TEST3'];
                                            var nodeCodes = ['A', 'B', 'C', 'D', 'E', 'F'];
                                            var table1s = ['A-4每日数据判读表', 'A1', 'A2', 'A3'];
                                            var table2s = ['2024-03-15', '2024-03-16', '2024-03-17'];
                                        
                                            var resData = [];
                                            for (var i = 0; i < 10; i++) {
                                                var data = {};
                                                var fileId = generateFileId();
                                                var fileName = '综合测试_' + fileId;
                                                var hasAttachment = getRandomValue(hasAttachments);
                                                var status = getRandomValue(statuss);
                                                var type = "00";
                                                var model = getRandomValue(models);
                                                var node = getRandomValue(nodes);
                                                var nodeCode = getRandomValue(nodeCodes);
                                                var table1 = getRandomValue(table1s);
                                                var table2 = getRandomValue(table2s);
                                                var splitVar = "#$";
                                                var resFileName = status + splitVar + type + splitVar + model + splitVar +
                                                    node + splitVar + nodeCode + splitVar + table1 + splitVar + table2 + splitVar + fileName + '.xlsx';
                                                data.name = resFileName;
                                                if (hasAttachment) {
                                                    type = '11';
                                                    //需要增加附件
                                                    var attachment = generateFileName();
                                                    var resAttachment = status + splitVar + type + "_" + attachment + splitVar + model + splitVar +
                                                        node + splitVar + nodeCode + splitVar + table1 + splitVar + table2 + splitVar + fileName + '.jpg';
                                                    data.attachment = resAttachment;
                                                }
                                                resData.push(data);
                                            }
                                            res.success = true;
                                            res.data = resData;
                                            res.msg = "CreateTestFile-创建测试文件夹-成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "CreateTestFile-创建测试文件夹-失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="CreateTestTemporaryFile">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    CreateTestTemporaryFile
                                         * @description   创建现场临时处理单的测试文件 date 2024年11月6日10:34:57
                                         * @implementation    {Script}
                                         *
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                        
                                            //随机生成10位数的文件编号
                                            function generateFileId() {
                                                var fileId = "";
                                                for (var i = 0; i < 10; i++) {
                                                    fileId += Math.floor(Math.random() * 10);
                                                }
                                                return fileId;
                                            }
                                        
                                            //随机生成文件名称
                                            function generateFileName() {
                                                var characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"; // 定义字符集
                                                var fileName = "";
                                                for (var i = 0; i < 8; i++) { // 文件名长度为8位
                                                    fileName += characters.charAt(Math.floor(Math.random() * characters.length)); // 从字符集中随机选择一个字符
                                                }
                                                return fileName;
                                            }
                                        
                                            function generateIndex(arrLen) {
                                                var randomInt = Math.floor(Math.random() * arrLen);
                                                var arrIndex = randomInt === arrLen ? 0 : randomInt;
                                                return arrIndex;
                                            }
                                        
                                            function getRandomValue(arr) {
                                                return arr[generateIndex(arr.length)];
                                            }
                                        
                                        
                                            var statuss = ['00', '11', '00'];
                                            var models = ["D1_1", "D1_3", "D1_4"];
                                            var nodes = ['现场临时处理单-TEST1'];
                                            var nodeCodes = ['A', 'B', 'C', 'D', 'E', 'F'];
                                            var table1s = ['现场临时处理单'];
                                            var table2s = ['2024-03-15'];
                                        
                                            var resData = [];
                                            for (var i = 0; i < 200; i++) {
                                                var data = {};
                                                var fileId = generateFileId();
                                                var fileName = '现场临时处理单_' + fileId;
                                                var status = getRandomValue(statuss);
                                                var type = "00";
                                                var model = getRandomValue(models);
                                                var node = getRandomValue(nodes);
                                                var nodeCode = getRandomValue(nodeCodes);
                                                var table1 = getRandomValue(table1s);
                                                var table2 = getRandomValue(table2s);
                                                var splitVar = "#$";
                                                var resFileName = status + splitVar + type + splitVar + model + splitVar +
                                                    node + splitVar + nodeCode + splitVar + table1 + splitVar + table2 + splitVar + fileName + '.xlsx';
                                                data.name = resFileName;
                                                resData.push(data);
                                            }
                                            res.success = true;
                                            res.data = resData;
                                            res.msg = "CreateTestTemporaryFile-创建测试文件夹-成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "CreateTestTemporaryFile-创建测试文件夹-失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="GetAllTestModel">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    GetAllTestModel
                                         * @description   获取所有的电测试系统的型号 date 2024/4/1 19:15
                                         * @implementation    {Script}
                                         *
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            var resData = [];
                                            var rs = Things['Thing.DB.Oracle'].RunQuery({sql: "select * from DATAPACKAGETREE where TEST_CODE is not null"}).rows;
                                            for (var i = 0; i < rs.length; i++) {
                                                var testCode = rs[i].TEST_CODE;
                                                if (testCode != " " && testCode != undefined && testCode != null && testCode != "") {
                                                    resData.push(testCode);
                                                }
                                            }
                                            res.success = true;
                                            res.data = resData;
                                            res.msg = "GetAllTestModel-获取所有的电测试系统的型号-成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "GetAllTestModel-获取所有的电测试系统的型号-失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="GetMappingNodeCode">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    GetMappingNodeCode
                                         * @description   date 2024/4/1 10:34
                                         * @implementation    {Script}
                                         *
                                         * @param    {STRING}    nodeCode    
                                         *
                                         * @returns    {STRING}
                                         */
                                        var res = "C阶段电测试";
                                        if (nodeCode == "A") {
                                            res = "A阶段电测试";
                                        } else if (nodeCode == "B") {
                                            res = "B阶段电测试";
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="GetTempCategory">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    GetTempCategory
                                         * @description   获取所有的现场临时处理单的分类 wanghq 2024年11月8日14:49:48
                                         * @implementation    {Script}
                                         *
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            //获取所有的分类
                                            var categoryRs = Things['Thing.DB.Oracle'].RunQuery({
                                                sql: "select t.*, ROW_NUMBER() over (ORDER BY DECODE(OPT_CATEGORY, '报警遥测波道屏蔽', 1, '遥测遥控指令变更', 2, '加载软件修改', 3, '变更程序、增减项目', 4) ) as ROWNO from (select distinct OPT_CATEGORY from OTP_ORDER) t"
                                            }).rows;
                                            var categorys = [];
                                            for (var i = 0; i < categoryRs.length; i++) {
                                                var category = categoryRs[i]['OPT_CATEGORY'];
                                                categorys.push(category);
                                            }
                                            res.success = true;
                                            res.data = categorys;
                                            res.msg = "成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "GetTempCategory失败，原因：" + error;
                                            logger.error(res.msg);
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="GetTempSql">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    GetTempSql
                                         * @description   获取查询临时处理单的sql wanghq 2024年11月7日16:19:00
                                         * @implementation    {Script}
                                         *
                                         * @param    {JSON}    query    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            var whereSql = "where 1=1"
                                            for (var queryKey in query) {
                                                if (query[queryKey] !== null && query[queryKey] !== undefined && query[queryKey] !== "" && query[queryKey] !== " ") {
                                                    if (queryKey === 'start-date' || queryKey === 'startDate') {
                                                        whereSql += " and OPT_DATE >= '" + query[queryKey] + "'";
                                                    } else if (queryKey === 'end-date' || queryKey === 'endDate') {
                                                        whereSql += " and OPT_DATE <= '" + query[queryKey] + "'";
                                                    } else if (queryKey === 'model_name') {
                                                        whereSql += " and MODEL_NAME = '" + query[queryKey] + "' ";
                                                    } else if (queryKey === 'treeId') {
                                                        if (query[queryKey] !== -1) {
                                                            whereSql += " and ( MODEL_ID = " + query[queryKey] + " or LEFT_ID = " + query[queryKey] + ")  ";
                                                        }
                                                    } else if (queryKey === 'username') {
                                                        var modelRs = Things['Thing.Fn.AitScreen'].GetUserRoleModel({ username: query[queryKey], isUseScreen: 1, treeId: -1 });
                                                        var modelArr = [];
                                                        for (var i = 0; i < modelRs.data.length; i++) {
                                                            modelArr.push(modelRs.data[i].TREEID);
                                                        }
                                                        var models = modelArr.join(",");
                                                        whereSql += " and MODEL_ID in (" + models + ") ";
                                                    } else if (queryKey === 'category') {
                                                        if (query[queryKey] !== '全部') {
                                                            whereSql += " and OPT_CATEGORY = '" + query[queryKey] + "' ";
                                                        }
                                                    }
                                                }
                                            }
                                            var sql = "select * from OTP_ORDER " + whereSql;
                                        
                                            res.success = true;
                                            res.data = sql;
                                            res.msg = "成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="GetTestEventSearchItem">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    GetTestEventSearchItem
                                         * @description   获取测试事件查询项 date 2024/4/18 16:11
                                         * @implementation    {Script}
                                         *
                                         * @param    {STRING}    modelName    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            var resData = {
                                                modelName: [],
                                                testItem: [],
                                                problemCategory: [],
                                                closureStatus: []
                                            };
                                            var testEventSql = me.GetTestEventSql().data;
                                        
                                            //查询测试项
                                            var testItemSql = "SELECT DISTINCT TEST_ITEM FROM MODEL_TEST_EVENTS where TEST_ITEM is not null";
                                            if (modelName !== null && modelName !== undefined && modelName !== "" && modelName !== " ") {
                                                resData.modelName.push(modelName);
                                                testItemSql = "SELECT DISTINCT TEST_ITEM FROM (" + testEventSql + ")";
                                                testItemSql += " where MODEL_NAME='" + modelName + "' and TEST_ITEM is not null";
                                            } else {
                                                //查询型号
                                                var modelSql = "SELECT DISTINCT MODEL_NAME FROM (" + testEventSql + ") where MODEL_NAME is not null order by MODEL_NAME";
                                                var modelRs = Things['Thing.DB.Oracle'].RunQuery({ sql: modelSql }).rows;
                                                for (var i = 0; i < modelRs.length; i++) {
                                                    resData.modelName.push(modelRs[i]['MODEL_NAME']);
                                                }
                                            }
                                            var testItemRs = Things['Thing.DB.Oracle'].RunQuery({ sql: testItemSql }).rows;
                                            for (var i = 0; i < testItemRs.length; i++) {
                                                resData.testItem.push(testItemRs[i]['TEST_ITEM']);
                                            }
                                            var problemCategorySql = "select t.*, " +
                                                "       ROW_NUMBER() over (ORDER BY DECODE(PROBLEM_CATEGORY, '星上产品', 1, '测试设计', 2, '测试实施', 3, '遥测报警', 4) ) as ROWNO " +
                                                "from (SELECT DISTINCT PROBLEM_CATEGORY FROM MODEL_TEST_EVENTS where PROBLEM_CATEGORY is not null) t";
                                            var problemCategoryRs = Things['Thing.DB.Oracle'].RunQuery({ sql: problemCategorySql }).rows;
                                            for (var i = 0; i < problemCategoryRs.length; i++) {
                                                resData.problemCategory.push(problemCategoryRs[i]['PROBLEM_CATEGORY']);
                                            }
                                            var closureStatusSql = "SELECT DISTINCT CLOSURE_STATUS FROM MODEL_TEST_EVENTS where CLOSURE_STATUS is not null";
                                            var closureStatusRs = Things['Thing.DB.Oracle'].RunQuery({ sql: closureStatusSql }).rows;
                                            for (var i = 0; i < closureStatusRs.length; i++) {
                                                resData.closureStatus.push(closureStatusRs[i]['CLOSURE_STATUS']);
                                            }
                                            res.success = true;
                                            res.data = resData;
                                            res.msg = "GetTestEventSearchItem--成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "GetTestEventSearchItem--失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="GetTestEventSql">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    GetTestEventSql
                                         * @description   date 2024/4/11 10:35
                                         * @implementation    {Script}
                                         *
                                         * @param    {JSON}    query    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            var whereSql = "where 1=1"
                                            for (var queryKey in query) {
                                                if (query[queryKey] !== null && query[queryKey] !== undefined && query[queryKey] !== "" && query[queryKey] !== " ") {
                                                    if (queryKey === 'start-date') {
                                                        whereSql += " and OCCURRENCE_TIME >= '" + query[queryKey] + " 00:00:00'";
                                                    } else if (queryKey === 'end-date') {
                                                        whereSql += " and OCCURRENCE_TIME <= '" + query[queryKey] + " 23:59:59'";
                                                    } else {
                                                        whereSql += " and " + queryKey + " = '" + query[queryKey] + "' ";
                                                    }
                                                }
                                            }
                                            var sql = "SELECT to_char(ROWNUM)  as rowno, a.* " +
                                                "from (SELECT * " +
                                                "FROM (SELECT A.ID, " +
                                                "             COALESCE(TO_CHAR(B.NODENAME), A.MODEL_NAME) AS MODEL_NAME, " +
                                                "             A.MODEL_NAME                                AS TEST_MODEL_NAME, " +
                                                "             NVL(A.STAGE_NAME, ' ')                      AS STAGE_NAME, " +
                                                "             NVL(A.TEST_ITEM, ' ')                       AS TEST_ITEM, " +
                                                "             NVL(A.CREATION_TIME, ' ')                   AS CREATION_TIME, " +
                                                "             NVL(A.REMARKS, ' ')                         AS REMARKS, " +
                                                "             NVL(A.CODE, ' ')                            AS CODE, " +
                                                "             NVL(A.OCCURRENCE_TIME, ' ')                 AS OCCURRENCE_TIME, " +
                                                "             NVL(A.EXCEPTION_PHENOMENON, ' ')            AS EXCEPTION_PHENOMENON, " +
                                                "             NVL(A.EXCEPTION_LOCATION, ' ')              AS EXCEPTION_LOCATION, " +
                                                "             NVL(A.EXCEPTION_PRODUCT_NAME, ' ')          AS EXCEPTION_PRODUCT_NAME, " +
                                                "             NVL(A.PRODUCT_CODE, ' ')                    AS PRODUCT_CODE, " +
                                                "             NVL(A.BATCH_OR_VERSION, ' ')                AS BATCH_OR_VERSION, " +
                                                "             NVL(A.RESPONSIBLE_UNIT, ' ')                AS RESPONSIBLE_UNIT, " +
                                                "             NVL(A.PHENOMENON_DESCRIPTION, ' ')          AS PHENOMENON_DESCRIPTION, " +
                                                "             NVL(A.PROBLEM_CATEGORY, ' ')                AS PROBLEM_CATEGORY, " +
                                                "             NVL(A.INITIAL_POSITION, ' ')                AS INITIAL_POSITION, " +
                                                "             NVL(A.HANDLING_MEASURES, ' ')               AS HANDLING_MEASURES, " +
                                                "             NVL(A.CONFIRMATION_509_18, ' ')             AS CONFIRMATION_509_18, " +
                                                "             NVL(A.CONFIRMATION_812_TEST, ' ')           AS CONFIRMATION_812_TEST, " +
                                                "             NVL(A.CLOSURE_STATUS, ' ')                  AS CLOSURE_STATUS, " +
                                                "             NVL(A.CLOSURE_DESCRIPTION, ' ')             AS CLOSURE_DESCRIPTION, " +
                                                "             NVL(A.CLOSURE_TIME, ' ')                    AS CLOSURE_TIME, " +
                                                "             NVL(A.CLOSURE_509_QUALITY, ' ')             AS CLOSURE_509_QUALITY, " +
                                                "             NVL(A.CLOSURE_812_QUALITY, ' ')             AS CLOSURE_812_QUALITY " +
                                                "      FROM MODEL_TEST_EVENTS A " +
                                                "               LEFT JOIN PHASE_MODEL B ON A.MODEL_NAME = B.TEST_CODE) " + whereSql +
                                                "ORDER BY OCCURRENCE_TIME DESC) a";
                                            res.success = true;
                                            res.data = sql;
                                            res.msg = "GetTestEventSql--成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "GetTestEventSql--失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="InitTemporaryTable">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    InitTemporaryTable
                                         * @description   初始化现场临时处理单表 wanghq 2024年11月7日15:13:34
                                         * @implementation    {Script}
                                         *
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            Things['Thing.DB.Oracle'].RunCommand({ sql: "delete from OTP_ORDER" });
                                            var dateArea = [2, 1];
                                            var categoryArea = [3, 1];
                                            var sql = "select T1.ID, T1.SAVE_DATA, t2.LEAF_ID, t2.LEAF_NAME, t2.LEAF_SORT, t3.TREEID, T3.NODENAME " +
                                                "from QUALITY_REPORT T1 " +
                                                "         LEFT JOIN TREE_ALL_VIEW T2 ON T1.TREE_ID = T2.TREEID " +
                                                "         LEFT JOIN PHASE_MODEL T3 ON T2.PHASE_ID = T3.TREEID " +
                                                "where T1.NAME like '%现场临时处理单%' " +
                                                "  and T1.IS_ELECTRIC_TEST = 1 " +
                                                "  and T1.IS_DELETE = 0 " +
                                                "  and t1.SAVE_DATA is not null";
                                            var num = 0;
                                            var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: sql });
                                            for (var i = 0; i < rs.rows.length; i++) {
                                                var row = rs.rows[i];
                                                var data = JSON.parse(row['SAVE_DATA']);
                                                var meta = data.meta;
                                                var leafId = row['LEAF_ID'];
                                                var leafName = row['LEAF_NAME'];
                                                var leafSort = row['LEAF_SORT'];
                                                var modelId = row['TREEID'];
                                                var modelName = row['NODENAME'];
                                                var reportId = row['ID'];
                                                var category = "";
                                                try {
                                                    category = data.tableData[categoryArea[0]][categoryArea[1]];
                                                } catch (error) {
                                                    category = "";
                                                }
                                                if (category.length > 15) {
                                                    category = "";
                                                }
                                                var date = "";
                                                try {
                                                    date = data.tableData[dateArea[0]][dateArea[1]];
                                                    parseDate(date, "yyyy-MM-dd");
                                                } catch (error) {
                                                    date = "";
                                                }
                                                var cell13e = "", cell13e_eles = [], cell13e_hasValue = false;
                                                var cell14e = "", cell14e_eles = [], cell14e_hasValue = false;
                                                var cell14g = "", cell14g_eles = [], cell14g_hasValue = false;
                                                for (var j = 0; j < meta.length; j++) {
                                                    if (meta[j].row == 12 && meta[j].col == 4) {
                                                        if (meta[j].eles) {
                                                            cell13e_eles = meta[j].eles;
                                                        }
                                                    }
                                                    if (meta[j].row == 13 && meta[j].col == 4) {
                                                        if (meta[j].eles) {
                                                            cell14e_eles = meta[j].eles;
                                                        }
                                                    }
                                                    if (meta[j].row == 13 && meta[j].col == 6) {
                                                        if (meta[j].eles) {
                                                            cell14g_eles = meta[j].eles;
                                                        }
                                                    }
                                                }
                                                try {
                                                    cell13e = data.tableData[12][4];
                                                } catch (error) {
                                                    cell13e = "";
                                                }
                                                try {
                                                    cell14e = data.tableData[13][4];
                                                } catch (error) {
                                                    cell14e = "";
                                                }
                                                try {
                                                    cell14g = data.tableData[13][6];
                                                } catch (error) {
                                                    cell14g = "";
                                                }
                                                if (cell13e || cell13e_eles.length > 0) {
                                                    cell13e_hasValue = true;
                                                }
                                                if (cell14e || cell14e_eles.length > 0) {
                                                    cell14e_hasValue = true;
                                                }
                                                if (cell14g || cell14g_eles.length > 0) {
                                                    cell14g_hasValue = true;
                                                }
                                                var isFinished = (cell13e_hasValue && cell14e_hasValue && cell14g_hasValue) ? 1 : 0;
                                                var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
                                                if (category !== "" && date !== "") {
                                                    var insertSql = "insert into OTP_ORDER (REPORT_ID, OPT_CATEGORY, OPT_DATE, LEFT_ID, LEFT_NAME, LEFT_SORT, MODEL_ID, MODEL_NAME, CREATE_TIME, IS_FINISHED)" +
                                                        "values (" + reportId + ", '" + category + "', '" + date + "', " + leafId + ", '" + leafName + "', " + leafSort + ", " + modelId + ", '" + modelName + "', '" + nowTime + "', " + isFinished + ")";
                                                    Things['Thing.DB.Oracle'].RunCommand({ sql: insertSql });
                                                    num++;
                                                }
                                            }
                                            res.success = true;
                                            res.msg = "成功插入" + num + "条数据";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "初始化现场临时处理单表失败，原因：" + error;
                                            logger.error(res.msg);
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="ManualSync">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    ManualSync
                                         * @description   手动同步 date 2024/3/29 15:00
                                         * @implementation    {Script}
                                         *
                                         * @param    {NUMBER}    treeId    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            var postData = {
                                                type: "null",
                                                model: "null",
                                                nodeCode: "null"
                                            };
                                            //查询该节点所在的初样正样阶段
                                            var phaseSql = "select * " +
                                                "from DATAPACKAGETREE " +
                                                "where NODETYPE = 'phase' " +
                                                "start with TREEID = " + treeId + " " +
                                                "connect by prior PARENTID = TREEID";
                                            var phaseRs = Things['Thing.DB.Oracle'].RunQuery({sql: phaseSql}).rows;
                                            if (phaseRs.length > 0) {
                                                if (phaseRs[0]['TEST_CODE']) {
                                                    postData.model = phaseRs[0]['TEST_CODE'];
                                                    //查询该节点的类型
                                                    var typeSql = "select * from DATAPACKAGETREE where TREEID = " + treeId;
                                                    var typeRs = Things['Thing.DB.Oracle'].RunQuery({sql: typeSql}).rows;
                                                    if (typeRs.length > 0) {
                                                        var type = typeRs[0]['NODETYPE'];
                                                        if (type == 'phase') {
                                                            postData.type = "model";
                                                        } else {
                                                            postData.nodeCode = typeRs[0]['NODENAME'];
                                                            postData.type = "nodeCode";
                                                        }
                                                        var params = {
                                                            url: Things['Thing.System'].GetFileHandleUrl() + "/electric/test/sync/create/file?type=" + postData.type + "&model=" + postData.model + "&nodeCode=" + postData.nodeCode,
                                                        };
                                                        var successNum = Resources["ContentLoaderFunctions"].PostText(params);
                                                        successNum = parseInt(successNum);
                                                        if (successNum > 0) {
                                                            res.success = true;
                                                            res.msg = "同步成功" + successNum + "个确表";
                                                        } else {
                                                            res.success = false;
                                                            res.msg = "未发现待同步的确认表！";
                                                        }
                                                    }
                                                } else {
                                                    res.success = false;
                                                    res.msg = "同步失败，原因：该节点所在阶段未关联电测试系统型号！";
                                                }
                                            } else {
                                                res.success = false;
                                                res.msg = "同步失败，原因：未找到该节点所在阶段";
                                            }
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "同步失败--失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="PostCreateFile">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    PostCreateFile
                                         * @description   wanghq 2025年5月22日9:55:52
                                         * @implementation    {Script}
                                         *
                                         *
                                         * @returns    {NOTHING}
                                         */
                                        var url = Things['Thing.System'].GetFileHandleUrl()+"/electric/test/create/file";
                                            var params = {
                                                url: url
                                            };
                                        Resources["ContentLoaderFunctions"].PostJSON(params);
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="QueryExportData">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    QueryExportData
                                         * @description   获取导出excel的数据 wanghq 2024年11月4日17:57:59
                                         * @implementation    {Script}
                                         *
                                         * @param    {JSON}    query    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            var resData = [];
                                        
                                            var basicSql = me.GetTestEventSql({ query: query }).data;
                                        
                                            var columns = [
                                                { colName: "ROWNO", colDisplayName: "序号", colWidth: 7 },
                                                { colName: "MODEL_NAME", colDisplayName: "卫星名称", colWidth: 20 },
                                                { colName: "STAGE_NAME", colDisplayName: "阶段名称", colWidth: 20 },
                                                { colName: "TEST_ITEM", colDisplayName: "测试项目", colWidth: 20 },
                                                { colName: "CREATION_TIME", colDisplayName: "创建时间", colWidth: 20 },
                                                { colName: "CODE", colDisplayName: "编号", colWidth: 20 },
                                                { colName: "OCCURRENCE_TIME", colDisplayName: "发生时刻", colWidth: 20 },
                                                { colName: "EXCEPTION_PHENOMENON", colDisplayName: "异常现象", colWidth: 20 },
                                                { colName: "EXCEPTION_LOCATION", colDisplayName: "异常发生地点", colWidth: 20 },
                                                { colName: "EXCEPTION_PRODUCT_NAME", colDisplayName: "异常产品名称", colWidth: 20 },
                                                { colName: "PRODUCT_CODE", colDisplayName: "产品代号", colWidth: 20 },
                                                { colName: "BATCH_OR_VERSION", colDisplayName: "批次号/版本号", colWidth: 20 },
                                                { colName: "RESPONSIBLE_UNIT", colDisplayName: "责任单位", colWidth: 20 },
                                                { colName: "PHENOMENON_DESCRIPTION", colDisplayName: "现象描述", colWidth: 35 },
                                                { colName: "PROBLEM_CATEGORY", colDisplayName: "问题分类", colWidth: 20 },
                                                { colName: "INITIAL_POSITION", colDisplayName: "初步定位", colWidth: 35 },
                                                { colName: "HANDLING_MEASURES", colDisplayName: "处理措施", colWidth: 35 },
                                                { colName: "CONFIRMATION_509_18", colDisplayName: "处置确认-509所十八室", colWidth: 24 },
                                                { colName: "CONFIRMATION_812_TEST", colDisplayName: "处置确认-812所测试中心", colWidth: 24 },
                                                { colName: "CLOSURE_STATUS", colDisplayName: "闭环情况", colWidth: 20 },
                                                { colName: "CLOSURE_DESCRIPTION", colDisplayName: "闭环情况描述", colWidth: 20 },
                                                { colName: "CLOSURE_TIME", colDisplayName: "闭环时间", colWidth: 20 },
                                                { colName: "CLOSURE_509_QUALITY", colDisplayName: "闭环情况-509所质量师", colWidth: 22 },
                                                { colName: "CLOSURE_812_QUALITY", colDisplayName: "闭环情况-812所质量师", colWidth: 22 },
                                                { colName: "REMARKS", colDisplayName: "备注", colWidth: 20 }
                                            ];
                                            var summaryData = Things['Thing.DB.Oracle'].RunQuery({ sql: basicSql }).ToJSON().rows;
                                            var summarySheet = {
                                                sheetName: "测试异常信息汇总(内部)",
                                                columns: columns,
                                                data: summaryData,
                                                dataSize: summaryData.length
                                            };
                                            resData.push(summarySheet);
                                            //获取所有的问题分类
                                            var problemCategorys = me.GetTestEventSearchItem().data.problemCategory;
                                            for (var i = 0; i < problemCategorys.length; i++) {
                                                var problemCategory = problemCategorys[i];
                                                var sql = "select to_char(ROWNUM) as rowno, x.*  from (" + basicSql + " where PROBLEM_CATEGORY='" + problemCategory + "') x";
                                                var problemCategoryData = Things['Thing.DB.Oracle'].RunQuery({
                                                    sql: sql
                                                }).ToJSON().rows;
                                                resData.push({
                                                    sheetName: problemCategory + "(内部)",
                                                    columns: columns,
                                                    data: problemCategoryData,
                                                    dataSize: problemCategoryData.length
                                                });
                                            }
                                            resData.push(me.QueryStatisticsDate({ query: query }).data);
                                            resData.push(me.QueryStatisticsModel({ query: query }).data);
                                            resData.push(me.QueryTempStatisticsModel({ query: query }).data);
                                        
                                            res.success = true;
                                            res.data = resData;
                                            res.msg = "成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "获取导出excel的数据-QueryExportData-失败，原因：" + error;
                                            logger.error(res.msg);
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="QueryStatisticsDate">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    QueryStatisticsDate
                                         * @description   按照时间每一天统计不同分类下的数量和占比 wanghq 2024年11月4日15:27:25
                                         * @implementation    {Script}
                                         *
                                         * @param    {JSON}    query    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            var basicSql = "(" + me.GetTestEventSql({ query: query }).data + ")";
                                            //获取所有的问题分类
                                            var problemCategorys = me.GetTestEventSearchItem().data.problemCategory;
                                            var columns = [{ colName: "日期", colDisplayName: "日期", colWidth: 20 }];
                                            var coalesceSql = "", sumSql = "", pivotSql = "(", toCharSql = "", totalChildSql = "", proportionChildSql = "";
                                            for (var i = 0; i < problemCategorys.length; i++) {
                                                var problemCategory = problemCategorys[i];
                                                var colDisplayName = '第' + Things['Thing.Fn.SecondTable'].NumToChinese({ num: (i + 1) }) + '类：' + problemCategory + "异常";
                                                columns.push({ colName: problemCategory, colDisplayName: colDisplayName, colWidth: 30 });
                                                coalesceSql += '  COALESCE(' + problemCategory + ', 0) as ' + problemCategory + ',';
                                                toCharSql += "TO_CHAR(" + problemCategory + ") as " + problemCategory + ",";
                                                totalChildSql += "TO_CHAR(SUM(" + problemCategory + ")),";
                                                proportionChildSql += "TO_CHAR(ROUND(100 * SUM(" + problemCategory + ") / SUM(合计), 2), 'FM90.0') || '%',";
                                                if (i == problemCategorys.length - 1) {
                                                    sumSql += '  COALESCE(' + problemCategory + ', 0)  as 合计';
                                                    pivotSql += "'" + problemCategory + "' as " + problemCategory + ")"
                                        
                                                } else {
                                                    sumSql += '  COALESCE(' + problemCategory + ', 0) +';
                                                    pivotSql += "'" + problemCategory + "' as " + problemCategory + ","
                                                }
                                            }
                                            columns.push({ colName: "合计", colDisplayName: "合计", colWidth: 12 });
                                            toCharSql += "TO_CHAR(合计) as 合计 ";
                                            totalChildSql += "TO_CHAR(SUM(合计))";
                                            proportionChildSql += "'100%'";
                                            var childSql = "SELECT OCCURRENCE_DATE, " + coalesceSql + sumSql + " " +
                                                "FROM (SELECT TRUNC(TO_DATE(OCCURRENCE_TIME, 'YYYY-MM-DD HH24:MI:SS')) AS OCCURRENCE_DATE, " +
                                                "             PROBLEM_CATEGORY, " +
                                                "             COUNT(*)                                               AS ISSUE_COUNT " +
                                                "      FROM " + basicSql + " " +
                                                "      WHERE PROBLEM_CATEGORY != ' ' " +
                                                "      GROUP BY TRUNC(TO_DATE(OCCURRENCE_TIME, 'YYYY-MM-DD HH24:MI:SS')), " +
                                                "               PROBLEM_CATEGORY) " +
                                                "    PIVOT ( " +
                                                "    SUM(ISSUE_COUNT) " +
                                                "    FOR PROBLEM_CATEGORY IN " + pivotSql + " " +
                                                "    ) " +
                                                "ORDER BY OCCURRENCE_DATE";
                                            var countSql = "SELECT TO_CHAR(OCCURRENCE_DATE, 'YYYY-MM-DD') AS 日期," + toCharSql + "from (" + childSql + ")";
                                            var totalSql = "SELECT '合计'," + totalChildSql + " from (" + childSql + ")";
                                            var proportionSql = "SELECT '占比'," + proportionChildSql + " from (" + childSql + ")";
                                            var sql = countSql + " union all " + totalSql + " union all " + proportionSql;
                                        
                                            var queryRes = Things['Thing.DB.Oracle'].RunQuery({ sql: sql });
                                            var rs = queryRes.ToJSON().rows;
                                            var finalData = [];
                                            if (rs.length > 2) {
                                                //补齐没有的日期
                                                var startDate = query['start-date'] || rs[0]['日期'];
                                                var endDate = query['end-date'] || rs[rs.length - 3]['日期'];
                                                //列出出开始日期和结束日期中的每一天 格式为yyyy-MM-dd
                                                var dateList = [];
                                                var startDateObj = new Date(startDate);
                                                var endDateObj = new Date(endDate);
                                                var day = 1000 * 60 * 60 * 24;
                                                while (startDateObj <= endDateObj) {
                                                    dateList.push(dateFormat(startDateObj, "yyyy-MM-dd"));
                                                    startDateObj.setTime(startDateObj.getTime() + day);
                                                }
                                        
                                        
                                                for (var i = 0; i < dateList.length; i++) {
                                                    var date = dateList[i];
                                                    var row = {}, exist = false;
                                                    for (var x = 0; x < rs.length; x++) {
                                                        if (date == rs[x]['日期']) {
                                                            exist = true;
                                                            row = JSON.parse(JSON.stringify(rs[x]));
                                                            break;
                                                        }
                                                    }
                                                    if (!exist) {
                                                        row['日期'] = date;
                                                        for (var j = 1; j < columns.length; j++) {
                                                            var colName = columns[j]['colName'];
                                                            row[colName] = "0";
                                                        }
                                                    }
                                                    finalData.push(row);
                                                }
                                                finalData.push(JSON.parse(JSON.stringify(rs[rs.length - 2])));
                                                finalData.push(JSON.parse(JSON.stringify(rs[rs.length - 1])));
                                            } else {
                                                finalData = rs;
                                            }
                                        
                                        
                                            var col1 = columns.length + 1;
                                            var row1 = 1;
                                            var col2 = col1 + 6;
                                            var row2 = row1 + 16;
                                            //计算饼图的信息
                                            var chart = {
                                                anchor: [0, 0, 0, 0, col1, row1, col2, row2],//dx1, dy1, dx2, dy2, col1, row1, col2, row2
                                                labelsAddress: [0, 0, 1, columns.length - 2],//firstRow, lastRow, firstCol, lastCol
                                                series: [{
                                                    valuesAddress: [finalData.length - 1, finalData.length - 1, 1, columns.length - 2],//firstRow, lastRow, firstCol, lastCol
                                                    name: ""
                                                }],
                                                type: "pie"
                                            };
                                        
                                            res.data = {
                                                dataSize: finalData.length - 2,
                                                data: finalData,
                                                columns: columns,
                                                sheetName: "异常信息统计表-时间维度(内部)",
                                                charts: [chart]
                                            };
                                            res.success = true;
                                            res.msg = "QueryStatisticsDate成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "QueryStatisticsDate失败，原因：" + error;
                                            logger.error(res.msg);
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="QueryStatisticsModel">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    QueryStatisticsModel
                                         * @description   获取型号统计的数据 wanghq 2024年11月4日17:16:01
                                         * @implementation    {Script}
                                         *
                                         * @param    {JSON}    query    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                        
                                            var basicSql = "(" + me.GetTestEventSql({ query: query }).data + ")";
                                            //获取所有的问题分类
                                            var problemCategorys = me.GetTestEventSearchItem().data.problemCategory;
                                            var columns = [{ colName: "MODEL_NAME", colDisplayName: "型号", colWidth: 15 }];
                                            for (var i = 0; i < problemCategorys.length; i++) {
                                                var colDisplayName = '第' + Things['Thing.Fn.SecondTable'].NumToChinese({ num: (i + 1) }) + '类：' + problemCategorys[i] + "异常";
                                                columns.push({ colName: problemCategorys[i], colDisplayName: colDisplayName, colWidth: 30 });
                                            }
                                        
                                            function generatePivotColumns(categories) {
                                                return categories.map(function (category) {
                                                    return "'" + category + "' AS " + category;
                                                }).join(", ");
                                            }
                                        
                                            function generateSelectStatement(categories) {
                                                var selects = categories.map(function (category) {
                                                    return "TO_CHAR(COALESCE(" + category + ", 0)) AS " + category + "";
                                                });
                                                selects.push("TO_CHAR(COALESCE(" + categories.map(function (category) {
                                                    return category;
                                                }).join(", 0) + COALESCE(") + ", 0)) AS 合计");
                                                selects.push("TO_CHAR(ROUND((COALESCE(" + categories.map(function (category) {
                                                    return category;
                                                }).join(", 0) + COALESCE(") + ", 0)) * 100 / NULLIF((SELECT SUM(COALESCE(" + categories.map(function (category) {
                                                    return category;
                                                }).join(", 0) + COALESCE(") + ", 0)) FROM (SELECT MODEL_NAME, PROBLEM_CATEGORY, COUNT(*) AS ISSUE FROM " + basicSql + " WHERE PROBLEM_CATEGORY IS NOT NULL GROUP BY MODEL_NAME, PROBLEM_CATEGORY) P PIVOT (SUM(ISSUE) FOR PROBLEM_CATEGORY IN (" + generatePivotColumns(categories) + "))), 0), 2), 'FM90.0') || '%' AS 占比");
                                                return selects.join(", ");
                                            }
                                        
                                            function generateUnionAllStatements(categories) {
                                                var unionAll = "UNION ALL SELECT '合计' AS MODEL_NAME," +
                                                    categories.map(function (category) {
                                                        return "TO_CHAR(SUM(COALESCE(" + category + ", 0))) AS " + category + "";
                                                    }).join(", ") + ", " +
                                                    "TO_CHAR(SUM(COALESCE(" + categories.map(function (category) {
                                                        return category;
                                                    }).join(", 0) + COALESCE(") + ", 0))) AS 合计, " +
                                                    "'100.0%' AS 占比 " +
                                                    "FROM (SELECT MODEL_NAME, PROBLEM_CATEGORY, COUNT(*) AS ISSUE FROM " + basicSql + " WHERE PROBLEM_CATEGORY IS NOT NULL GROUP BY MODEL_NAME, PROBLEM_CATEGORY) P PIVOT (SUM(ISSUE) FOR PROBLEM_CATEGORY IN (" + generatePivotColumns(categories) + ")) " +
                                                    "UNION ALL SELECT '占比' AS MODEL_NAME," +
                                                    categories.map(function (category) {
                                                        return "TO_CHAR(ROUND(SUM(COALESCE(" + category + ", 0)) * 100 / NULLIF(SUM(COALESCE(" + categories.map(function (category) {
                                                            return category;
                                                        }).join(", 0) + COALESCE(") + ", 0)), 0), 2), 'FM90.0') || '%' AS " + category + "";
                                                    }).join(", ") + ", " +
                                                    "'100.0%' AS 合计, " +
                                                    "'100.0%' AS 占比 " +
                                                    "FROM (SELECT MODEL_NAME, PROBLEM_CATEGORY, COUNT(*) AS ISSUE FROM " + basicSql + " WHERE PROBLEM_CATEGORY IS NOT NULL GROUP BY MODEL_NAME, PROBLEM_CATEGORY) P PIVOT (SUM(ISSUE) FOR PROBLEM_CATEGORY IN (" + generatePivotColumns(categories) + ")) ";
                                                return unionAll;
                                            }
                                        
                                            var sql = "SELECT MODEL_NAME," +
                                                generateSelectStatement(problemCategorys) + " " +
                                                "FROM (SELECT MODEL_NAME, PROBLEM_CATEGORY, COUNT(*) AS ISSUE FROM " + basicSql + " WHERE PROBLEM_CATEGORY IS NOT NULL GROUP BY MODEL_NAME, PROBLEM_CATEGORY) P PIVOT (SUM(ISSUE) FOR PROBLEM_CATEGORY IN (" + generatePivotColumns(problemCategorys) + ")) " +
                                                generateUnionAllStatements(problemCategorys);
                                            var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: sql }).ToJSON().rows;
                                        
                                            columns.push({ colName: '合计', colDisplayName: '合计', colWidth: 12 });
                                            columns.push({ colName: '占比', colDisplayName: '占比', colWidth: 12 });
                                        
                                            var col1 = columns.length + 1;
                                            var row1 = 1;
                                            var col2 = col1 + 6;
                                            var row2 = row1 + 16;
                                        
                                            //计算饼图的信息
                                            var pieChart = {
                                                anchor: [0, 0, 0, 0, col1, row1, col2, row2],//dx1, dy1, dx2, dy2, col1, row1, col2, row2
                                                labelsAddress: [1, rs.length - 2, 0, 0],//firstRow, lastRow, firstCol, lastCol
                                                series: [{
                                                    valuesAddress: [1, rs.length - 2, columns.length - 2, columns.length - 2],//firstRow, lastRow, firstCol, lastCol
                                                    name: ""
                                                }],
                                                type: "pie"
                                            };
                                        
                                            var lineChart = {
                                                anchor: [0, 0, 0, 0, 0, rs.length + 10, columns.length, rs.length + 20],//dx1, dy1, dx2, dy2, col1, row1, col2, row2
                                                labelsAddress: [1, rs.length - 2, 0, 0],//firstRow, lastRow, firstCol, lastCol
                                                type: "line"
                                            };
                                            var series = [];
                                            for (var i = 1; i < columns.length - 1; i++) {
                                                series.push({
                                                    name: columns[i].colDisplayName,
                                                    valuesAddress: [1, rs.length - 2, i, i]
                                                });
                                            }
                                            lineChart.series = series;
                                        
                                            res.success = true;
                                            res.data = {
                                                data: rs,
                                                dataSize: rs.length - 2,
                                                columns: columns,
                                                sheetName: "异常信息统计表-型号维度(内部)",
                                                charts: [pieChart, lineChart]
                                            };
                                            res.msg = "成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "QueryStatisticsModel失败，原因：" + error;
                                            logger.error(res.msg);
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="QueryTempCompletionStatus">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    QueryTempCompletionStatus
                                         * @description   获取临时处理单的完成状态  wanghq 2025年5月22日9:55:52
                                         * @implementation    {Script}
                                         *
                                         * @param    {JSON}    query    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            var categorys = me.GetTempCategory().data;
                                            categorys.push('现场技术问题处理单');
                                            
                                            var basicSql = "(" + me.GetTempSql({ query: query }).data + ")";
                                            
                                            // 构建查询已完成和未完成数量的SQL
                                            var sql = "SELECT MODEL_NAME, MODEL_ID, OPT_CATEGORY, IS_FINISHED, COUNT(*) AS COUNT " +
                                                      "FROM " + basicSql + " " +
                                                      "WHERE OPT_CATEGORY IS NOT NULL " +
                                                      "GROUP BY MODEL_NAME, MODEL_ID, OPT_CATEGORY, IS_FINISHED";
                                            
                                            var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: sql }).ToJSON().rows;
                                            
                                            // 用于存储结果的对象
                                            var statusData = {};
                                            
                                            // 处理查询结果
                                            for (var i = 0; i < rs.length; i++) {
                                                var row = rs[i];
                                                var modelKey = row.MODEL_NAME + '~~~' + row.MODEL_ID;
                                                var category = row.OPT_CATEGORY;
                                                var isFinished = row.IS_FINISHED;
                                                var count = parseInt(row.COUNT);
                                                
                                                // 确保模型键存在
                                                if (!statusData[modelKey]) {
                                                    statusData[modelKey] = {};
                                                }
                                                
                                                // 确保分类键存在
                                                if (!statusData[modelKey][category]) {
                                                    statusData[modelKey][category] = {
                                                        finished: 0,
                                                        unfinished: 0,
                                                        total: 0
                                                    };
                                                }
                                                
                                                // 更新数量
                                                if (isFinished === 1) {
                                                    statusData[modelKey][category].finished += count;
                                                } else {
                                                    statusData[modelKey][category].unfinished += count;
                                                }
                                                
                                                statusData[modelKey][category].total += count;
                                            }
                                            
                                            // 添加全部分类的统计数据
                                            for (var modelKey in statusData) {
                                                var modelData = statusData[modelKey];
                                                var totalFinished = 0;
                                                var totalUnfinished = 0;
                                                
                                                for (var category in modelData) {
                                                    totalFinished += modelData[category].finished;
                                                    totalUnfinished += modelData[category].unfinished;
                                                }
                                                
                                                modelData['全部'] = {
                                                    finished: totalFinished,
                                                    unfinished: totalUnfinished,
                                                    total: totalFinished + totalUnfinished
                                                };
                                            }
                                            
                                            res.success = true;
                                            res.data = statusData;
                                            res.msg = "成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "获取完成状态失败，原因：" + error;
                                            logger.error(res.msg);
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="QueryTempStatisticsModel">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    QueryTempStatisticsModel
                                         * @description   获取现场临时处理单的型号统计数据 wanghq 2024年11月7日16:08:01
                                         * @implementation    {Script}
                                         *
                                         * @param    {JSON}    query    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                        
                                            var basicSql = "(" + me.GetTempSql({ query: query }).data + ")";
                                            //获取所有的分类
                                            var categorys = me.GetTempCategory().data;
                                            var columns = [{ colName: "MODEL_NAME", colDisplayName: "型号", colWidth: 15 }];
                                            for (var i = 0; i < categorys.length; i++) {
                                                var category = categorys[i];
                                                var colDisplayName = category;
                                                columns.push({ colName: category, colDisplayName: colDisplayName, colWidth: 30 });
                                            }
                                            categorys.push('现场技术问题处理单');
                                            columns.push({ colName: '现场技术问题处理单', colDisplayName: '现场技术问题处理单(设计电测)', colWidth: 30 });
                                        
                                        
                                            function generatePivotColumns(categories) {
                                                return categories.map(function (category) {
                                                    return "'" + category + "' AS \"" + category + "\"";
                                                }).join(", ");
                                            }
                                        
                                            function generateSelectStatement(categories) {
                                                var selects = categories.map(function (category) {
                                                    return "TO_CHAR(COALESCE(\"" + category + "\", 0)) AS \"" + category + "\"";
                                                });
                                                selects.push("TO_CHAR(COALESCE(\"" + categories.map(function (category) {
                                                    return category;
                                                }).join("\", 0) + COALESCE(\"") + "\", 0)) AS 合计");
                                        
                                                selects.push("TO_CHAR(ROUND((COALESCE(\"" + categories.map(function (category) {
                                                    return category;
                                                }).join("\", 0) + COALESCE(\"") + "\", 0)) * 100 / NULLIF((SELECT SUM(COALESCE(\"" + categories.map(function (category) {
                                                    return category;
                                                }).join("\", 0) + COALESCE(\"") + "\", 0)) FROM (SELECT MODEL_NAME, MODEL_ID, OPT_CATEGORY, COUNT(*) AS ISSUE FROM "
                                                    + basicSql +
                                                    " WHERE OPT_CATEGORY IS NOT NULL GROUP BY MODEL_NAME, MODEL_ID, OPT_CATEGORY) P PIVOT (SUM(ISSUE) FOR OPT_CATEGORY IN ("
                                                    + generatePivotColumns(categories) + "))), 0), 2), 'FM90.0') || '%' AS 占比");
                                                return selects.join(", ");
                                            }
                                        
                                            function generateUnionAllStatements(categories) {
                                                var unionAll = "UNION ALL SELECT '合计' AS MODEL_NAME, 0 AS MODEL_ID ," +
                                                    categories.map(function (category) {
                                                        return "TO_CHAR(SUM(COALESCE(\"" + category + "\", 0))) AS \"" + category + "\"";
                                                    }).join(", ") + ", " +
                                                    "TO_CHAR(SUM(COALESCE(\"" + categories.map(function (category) {
                                                        return category;
                                                    }).join("\", 0) + COALESCE(\"") + "\", 0))) AS 合计, " +
                                                    "'100.0%' AS 占比 " +
                                                    "FROM (SELECT MODEL_NAME, MODEL_ID, OPT_CATEGORY, COUNT(*) AS ISSUE FROM " + basicSql + " WHERE OPT_CATEGORY IS NOT NULL GROUP BY MODEL_NAME, MODEL_ID, OPT_CATEGORY) P PIVOT (SUM(ISSUE) FOR OPT_CATEGORY IN (" + generatePivotColumns(categories) + ")) ";
                                                return unionAll;
                                            }
                                        
                                            var sql = "SELECT MODEL_NAME, MODEL_ID," +
                                                generateSelectStatement(categorys) + " " +
                                                "FROM (SELECT MODEL_NAME, MODEL_ID, OPT_CATEGORY, COUNT(*) AS ISSUE FROM " + basicSql + " WHERE OPT_CATEGORY IS NOT NULL GROUP BY MODEL_NAME, MODEL_ID, OPT_CATEGORY) P PIVOT (SUM(ISSUE) FOR OPT_CATEGORY IN (" + generatePivotColumns(categorys) + ")) " +
                                                generateUnionAllStatements(categorys);
                                            var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: sql }).ToJSON().rows;
                                        
                                            columns.push({ colName: '合计', colDisplayName: '合计', colWidth: 12 });
                                        
                                            res.success = true;
                                            res.data = {
                                                data: rs,
                                                dataSize: rs.length - 1,
                                                columns: columns,
                                                sheetName: "电测试现场异常处置单据统计(内部)",
                                                categorys: categorys
                                            };
                                            res.msg = "成功";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "QueryStatisticsModel失败，原因：" + error;
                                            logger.error(res.msg);
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="QueryTestEvent">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    QueryTestEvent
                                         * @description   date 2024/4/11 10:40
                                         * @implementation    {Script}
                                         *
                                         * @param    {JSON}    query    
                                         * @param    {INTEGER}    page    
                                         * @param    {INTEGER}    limit    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                        
                                            var startRowno = (page - 1) * limit + 1;
                                            var endRowno = page * limit;
                                        
                                            var sql = me.GetTestEventSql({ query: query }).data;
                                            var selectPageSql = "select * from (select s.* from (" + sql + ") s) where  rowno >= " + startRowno + " and  rowno <=" + endRowno;
                                            var pageData = Things["Thing.DB.Oracle"].RunQuery({ sql: selectPageSql });
                                            var selectCountSql = "select count(*) count from (" + sql + ") t ";
                                        
                                            res.count = Things["Thing.DB.Oracle"].RunQuery({ sql: selectCountSql }).rows[0]["COUNT"];
                                            res.data = pageData.ToJSON().rows;
                                            res.code = 0;
                                            res.msg = "QueryTestEvent--成功";
                                        } catch (error) {
                                            res.code = 500;
                                            var msg = "QueryTestEvent-查询失败，原因：" + error;
                                            res.msg = msg;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="SaveTestTable">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    SaveTestTable
                                         * @description   date 2024/3/28 10:53
                                         * @implementation    {Script}
                                         *
                                         * @param    {STRING}    fileStr    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            /*
                                            {
                                              "fileName": "00#$00#$D1_1#$TEST2#$电测试#$A2#$2024-03-15#$综合测试_6787972715.xlsx",
                                              "attachments": [
                                                {
                                                  "fileName": "usiw3ekr",
                                                  "filePath": "2024-03\\cd1a6224-b06c-4e7a-97f0-ec386fbc3d25",
                                                  "fileSize": "127.56 KB",
                                                  "createTime": "2024-03-28 10:46:40",
                                                  "fileFormat": "jpg"
                                                }
                                              ],
                                              "nodeCode": "电测试",
                                              "filePath": "E:\\DataPkgFile\\test\\00#$00#$D1_1#$TEST2#$电测试#$A2#$2024-03-15#$综合测试_6787972715.xlsx",
                                              "tableData": "{\"merged\":[{\"col\":0,\"colspan\":9,\"removed\":false,\"rowspan\":1,\"row\":0},{\"col\":0,\"colspan\":2,\"removed\":false,\"rowspan\":1,\"row\":1},{\"col\":2,\"colspan\":4,\"removed\":false,\"rowspan\":1,\"row\":1},{\"col\":7,\"colspan\":2,\"removed\":false,\"rowspan\":1,\"row\":1},{\"col\":0,\"colspan\":2,\"removed\":false,\"rowspan\":1,\"row\":2},{\"col\":2,\"colspan\":4,\"removed\":false,\"rowspan\":1,\"row\":2},{\"col\":7,\"colspan\":2,\"removed\":false,\"rowspan\":1,\"row\":2},{\"col\":0,\"colspan\":2,\"removed\":false,\"rowspan\":1,\"row\":16},{\"col\":2,\"colspan\":7,\"removed\":false,\"rowspan\":1,\"row\":16},{\"col\":0,\"colspan\":2,\"removed\":false,\"rowspan\":1,\"row\":17},{\"col\":2,\"colspan\":7,\"removed\":false,\"rowspan\":1,\"row\":17},{\"col\":0,\"colspan\":2,\"removed\":false,\"rowspan\":1,\"row\":18},{\"col\":2,\"colspan\":3,\"removed\":false,\"rowspan\":1,\"row\":18},{\"col\":6,\"colspan\":3,\"removed\":false,\"rowspan\":1,\"row\":18},{\"col\":2,\"colspan\":7,\"removed\":false,\"rowspan\":1,\"row\":19},{\"col\":2,\"colspan\":7,\"removed\":false,\"rowspan\":1,\"row\":20},{\"col\":2,\"colspan\":7,\"removed\":false,\"rowspan\":1,\"row\":21},{\"col\":0,\"colspan\":1,\"removed\":false,\"rowspan\":13,\"row\":3},{\"col\":0,\"colspan\":2,\"removed\":false,\"rowspan\":3,\"row\":19}],\"tableData\":[[\"供配电子系统_20240129000000\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[\"测试项目\",\"\",\"遥测精度检查\",\"\",\"\",\"\",\"测试地点\",\"910C\",\"\"],[\"测试时间\",\"\",\"2024-01-29 08:00:00 ~ 2024-01-29 18:00:00\",\"\",\"\",\"\",\"测试环境\",\"19°38%\",\"\"],[\"主要遥测参数\",\"序号\",\"参数代号\",\"参数名称\",\"指标要求\",\"验收值\",\"上次测试值\",\"本次测试值\",\"符合性\"],[\"\",\"1\",\"TMV1006\",\"A组BDR1输入电流\",\"正常:[0,25]\",\"\",\"正常:[0,0]\",\"正常:[0,0]\",\"符合\"],[\"\",\"2\",\"TMV1007\",\"A组BDR2输入电流\",\"正常:[0,25]\",\"\",\"正常:[0.16,0.16]\",\"正常:[0.16,0.16]\",\"符合\"],[\"\",\"3\",\"TMV1008\",\"A组BDR3输入电流\",\"正常:[0,25]\",\"\",\"正常:[0.15,0.15]\",\"正常:[0.15,0.15]\",\"符合\"],[\"\",\"4\",\"TMV1009\",\"A组BDR4输入电流\",\"正常:[0,25]\",\"\",\"正常:[0.14,0.14]\",\"正常:[0.14,0.14]\",\"符合\"],[\"\",\"5\",\"TMV1010\",\"B组BDR1输入电流\",\"正常:[0,25]\",\"\",\"正常:[0.3,0.3]\",\"正常:[0.3,0.3]\",\"符合\"],[\"\",\"6\",\"TMV1011\",\"B组BDR2输入电流\",\"正常:[0,25]\",\"\",\"正常:[0.21,0.21]\",\"正常:[0.21,0.21]\",\"符合\"],[\"\",\"7\",\"TMV1012\",\"B组BDR3输入电流\",\"正常:[0,25]\",\"\",\"正常:[0.19,0.19]\",\"正常:[0.19,0.19]\",\"符合\"],[\"\",\"8\",\"TMV1013\",\"B组BDR4输入电流\",\"正常:[0,25]\",\"\",\"正常:[0.11,0.11]\",\"正常:[0.11,0.11]\",\"符合\"],[\"\",\"9\",\"TMV1014\",\"A组BCR1输出电流\",\"正常:[0,25]\",\"\",\"正常:[0.3,0.3]\",\"正常:[0.3,0.3]\",\"符合\"],[\"\",\"10\",\"TMV1015\",\"A组BCR2输出电流\",\"正常:[0,25]\",\"\",\"正常:[0.4,0.4]\",\"正常:[0.4,0.4]\",\"符合\"],[\"\",\"11\",\"TMV1016\",\"B组BCR1输出电流\",\"正常:[0,25]\",\"\",\"正常:[0.16,0.16]\",\"正常:[0.16,0.16]\",\"符合\"],[\"\",\"12\",\"TMV1017\",\"B组BCR2输出电流\",\"正常:[0,25]\",\"\",\"正常:[0.19,0.19]\",\"正常:[0.19,0.19]\",\"符合\"],[\"异常情况及处理记录\",\"\",\"无\",\"\",\"\",\"\",\"\",\"\",\"\"],[\"测试结论\",\"\",\"正常\",\"\",\"\",\"\",\"\",\"\",\"\"],[\"测试人\",\"\",\"\",\"\",\"\",\"确认人\",\"\",\"\",\"\"],[\"重要曲线\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"]],\"meta\":[{\"col\":0,\"eles\":[],\"row\":0},{\"col\":1,\"eles\":[],\"row\":0},{\"col\":2,\"eles\":[],\"row\":0},{\"col\":3,\"eles\":[],\"row\":0},{\"col\":4,\"eles\":[],\"row\":0},{\"col\":5,\"eles\":[],\"row\":0},{\"col\":6,\"eles\":[],\"row\":0},{\"col\":7,\"eles\":[],\"row\":0},{\"col\":8,\"eles\":[],\"row\":0},{\"col\":0,\"eles\":[],\"row\":1},{\"col\":1,\"eles\":[],\"row\":1},{\"col\":2,\"eles\":[],\"row\":1},{\"col\":3,\"eles\":[],\"row\":1},{\"col\":4,\"eles\":[],\"row\":1},{\"col\":5,\"eles\":[],\"row\":1},{\"col\":6,\"eles\":[],\"row\":1},{\"col\":7,\"eles\":[],\"row\":1},{\"col\":8,\"eles\":[],\"row\":1},{\"col\":0,\"eles\":[],\"row\":2},{\"col\":1,\"eles\":[],\"row\":2},{\"col\":2,\"eles\":[],\"row\":2},{\"col\":3,\"eles\":[],\"row\":2},{\"col\":4,\"eles\":[],\"row\":2},{\"col\":5,\"eles\":[],\"row\":2},{\"col\":6,\"eles\":[],\"row\":2},{\"col\":7,\"eles\":[],\"row\":2},{\"col\":8,\"eles\":[],\"row\":2},{\"col\":0,\"eles\":[],\"row\":3},{\"col\":1,\"eles\":[],\"row\":3},{\"col\":2,\"eles\":[],\"row\":3},{\"col\":3,\"eles\":[],\"row\":3},{\"col\":4,\"eles\":[],\"row\":3},{\"col\":5,\"eles\":[],\"row\":3},{\"col\":6,\"eles\":[],\"row\":3},{\"col\":7,\"eles\":[],\"row\":3},{\"col\":8,\"eles\":[],\"row\":3},{\"col\":0,\"eles\":[],\"row\":4},{\"col\":1,\"eles\":[],\"row\":4},{\"col\":2,\"eles\":[],\"row\":4},{\"col\":3,\"eles\":[],\"row\":4},{\"col\":4,\"eles\":[],\"row\":4},{\"col\":5,\"eles\":[],\"row\":4},{\"col\":6,\"eles\":[],\"row\":4},{\"col\":7,\"eles\":[],\"row\":4},{\"col\":8,\"eles\":[],\"row\":4},{\"col\":0,\"eles\":[],\"row\":5},{\"col\":1,\"eles\":[],\"row\":5},{\"col\":2,\"eles\":[],\"row\":5},{\"col\":3,\"eles\":[],\"row\":5},{\"col\":4,\"eles\":[],\"row\":5},{\"col\":5,\"eles\":[],\"row\":5},{\"col\":6,\"eles\":[],\"row\":5},{\"col\":7,\"eles\":[],\"row\":5},{\"col\":8,\"eles\":[],\"row\":5},{\"col\":0,\"eles\":[],\"row\":6},{\"col\":1,\"eles\":[],\"row\":6},{\"col\":2,\"eles\":[],\"row\":6},{\"col\":3,\"eles\":[],\"row\":6},{\"col\":4,\"eles\":[],\"row\":6},{\"col\":5,\"eles\":[],\"row\":6},{\"col\":6,\"eles\":[],\"row\":6},{\"col\":7,\"eles\":[],\"row\":6},{\"col\":8,\"eles\":[],\"row\":6},{\"col\":0,\"eles\":[],\"row\":7},{\"col\":1,\"eles\":[],\"row\":7},{\"col\":2,\"eles\":[],\"row\":7},{\"col\":3,\"eles\":[],\"row\":7},{\"col\":4,\"eles\":[],\"row\":7},{\"col\":5,\"eles\":[],\"row\":7},{\"col\":6,\"eles\":[],\"row\":7},{\"col\":7,\"eles\":[],\"row\":7},{\"col\":8,\"eles\":[],\"row\":7},{\"col\":0,\"eles\":[],\"row\":8},{\"col\":1,\"eles\":[],\"row\":8},{\"col\":2,\"eles\":[],\"row\":8},{\"col\":3,\"eles\":[],\"row\":8},{\"col\":4,\"eles\":[],\"row\":8},{\"col\":5,\"eles\":[],\"row\":8},{\"col\":6,\"eles\":[],\"row\":8},{\"col\":7,\"eles\":[],\"row\":8},{\"col\":8,\"eles\":[],\"row\":8},{\"col\":0,\"eles\":[],\"row\":9},{\"col\":1,\"eles\":[],\"row\":9},{\"col\":2,\"eles\":[],\"row\":9},{\"col\":3,\"eles\":[],\"row\":9},{\"col\":4,\"eles\":[],\"row\":9},{\"col\":5,\"eles\":[],\"row\":9},{\"col\":6,\"eles\":[],\"row\":9},{\"col\":7,\"eles\":[],\"row\":9},{\"col\":8,\"eles\":[],\"row\":9},{\"col\":0,\"eles\":[],\"row\":10},{\"col\":1,\"eles\":[],\"row\":10},{\"col\":2,\"eles\":[],\"row\":10},{\"col\":3,\"eles\":[],\"row\":10},{\"col\":4,\"eles\":[],\"row\":10},{\"col\":5,\"eles\":[],\"row\":10},{\"col\":6,\"eles\":[],\"row\":10},{\"col\":7,\"eles\":[],\"row\":10},{\"col\":8,\"eles\":[],\"row\":10},{\"col\":0,\"eles\":[],\"row\":11},{\"col\":1,\"eles\":[],\"row\":11},{\"col\":2,\"eles\":[],\"row\":11},{\"col\":3,\"eles\":[],\"row\":11},{\"col\":4,\"eles\":[],\"row\":11},{\"col\":5,\"eles\":[],\"row\":11},{\"col\":6,\"eles\":[],\"row\":11},{\"col\":7,\"eles\":[],\"row\":11},{\"col\":8,\"eles\":[],\"row\":11},{\"col\":0,\"eles\":[],\"row\":12},{\"col\":1,\"eles\":[],\"row\":12},{\"col\":2,\"eles\":[],\"row\":12},{\"col\":3,\"eles\":[],\"row\":12},{\"col\":4,\"eles\":[],\"row\":12},{\"col\":5,\"eles\":[],\"row\":12},{\"col\":6,\"eles\":[],\"row\":12},{\"col\":7,\"eles\":[],\"row\":12},{\"col\":8,\"eles\":[],\"row\":12},{\"col\":0,\"eles\":[],\"row\":13},{\"col\":1,\"eles\":[],\"row\":13},{\"col\":2,\"eles\":[],\"row\":13},{\"col\":3,\"eles\":[],\"row\":13},{\"col\":4,\"eles\":[],\"row\":13},{\"col\":5,\"eles\":[],\"row\":13},{\"col\":6,\"eles\":[],\"row\":13},{\"col\":7,\"eles\":[],\"row\":13},{\"col\":8,\"eles\":[],\"row\":13},{\"col\":0,\"eles\":[],\"row\":14},{\"col\":1,\"eles\":[],\"row\":14},{\"col\":2,\"eles\":[],\"row\":14},{\"col\":3,\"eles\":[],\"row\":14},{\"col\":4,\"eles\":[],\"row\":14},{\"col\":5,\"eles\":[],\"row\":14},{\"col\":6,\"eles\":[],\"row\":14},{\"col\":7,\"eles\":[],\"row\":14},{\"col\":8,\"eles\":[],\"row\":14},{\"col\":0,\"eles\":[],\"row\":15},{\"col\":1,\"eles\":[],\"row\":15},{\"col\":2,\"eles\":[],\"row\":15},{\"col\":3,\"eles\":[],\"row\":15},{\"col\":4,\"eles\":[],\"row\":15},{\"col\":5,\"eles\":[],\"row\":15},{\"col\":6,\"eles\":[],\"row\":15},{\"col\":7,\"eles\":[],\"row\":15},{\"col\":8,\"eles\":[],\"row\":15},{\"col\":0,\"eles\":[],\"row\":16},{\"col\":1,\"eles\":[],\"row\":16},{\"col\":2,\"eles\":[],\"row\":16},{\"col\":3,\"eles\":[],\"row\":16},{\"col\":4,\"eles\":[],\"row\":16},{\"col\":5,\"eles\":[],\"row\":16},{\"col\":6,\"eles\":[],\"row\":16},{\"col\":7,\"eles\":[],\"row\":16},{\"col\":8,\"eles\":[],\"row\":16},{\"col\":0,\"eles\":[],\"row\":17},{\"col\":1,\"eles\":[],\"row\":17},{\"col\":2,\"eles\":[],\"row\":17},{\"col\":3,\"eles\":[],\"row\":17},{\"col\":4,\"eles\":[],\"row\":17},{\"col\":5,\"eles\":[],\"row\":17},{\"col\":6,\"eles\":[],\"row\":17},{\"col\":7,\"eles\":[],\"row\":17},{\"col\":8,\"eles\":[],\"row\":17},{\"col\":0,\"eles\":[],\"row\":18},{\"col\":1,\"eles\":[],\"row\":18},{\"col\":2,\"readOnly\":true,\"eles\":[{\"date\":\"2024-03-11\",\"col\":2,\"src\":\"//2024-03//c8e86c00-6ea2-4633-953a-368de2d30a8d\",\"type\":\"sign\",\"row\":18,\"class\":\"sign-img test-sign\"}],\"row\":18},{\"col\":3,\"eles\":[],\"row\":18},{\"col\":4,\"eles\":[],\"row\":18},{\"col\":5,\"eles\":[],\"row\":18},{\"col\":6,\"readOnly\":true,\"eles\":[{\"date\":\"2024-03-08\",\"col\":6,\"src\":\"//2024-03//6b07d098-5cdc-4d1e-aa47-bd8569db6b33\",\"type\":\"sign\",\"row\":18,\"class\":\"sign-img test-sign\"}],\"row\":18},{\"col\":7,\"eles\":[],\"row\":18},{\"col\":8,\"eles\":[],\"row\":18},{\"col\":0,\"eles\":[],\"row\":19},{\"col\":1,\"eles\":[],\"row\":19},{\"col\":2,\"eles\":[{\"date\":\"2024-03-28\",\"col\":2,\"src\":\"/File//2024-03//ae89465f-80a2-4208-abda-6823a33671b9\",\"photoPath\":\"//2024-03//ae89465f-80a2-4208-abda-6823a33671b9\",\"type\":\"photo\",\"photoName\":\"19行,2列\",\"photoFormat\":\"png\",\"photoShowNum\":\"图1\",\"row\":19,\"class\":\"sign-img photo\"}],\"row\":19},{\"col\":3,\"eles\":[],\"row\":19},{\"col\":4,\"eles\":[],\"row\":19},{\"col\":5,\"eles\":[],\"row\":19},{\"col\":6,\"eles\":[],\"row\":19},{\"col\":7,\"eles\":[],\"row\":19},{\"col\":8,\"eles\":[],\"row\":19},{\"col\":0,\"eles\":[],\"row\":20},{\"col\":1,\"eles\":[],\"row\":20},{\"col\":2,\"eles\":[{\"date\":\"2024-03-28\",\"col\":2,\"src\":\"/File//2024-03//df6908e7-40c5-41e2-bf3c-699d808d5faa\",\"photoPath\":\"//2024-03//df6908e7-40c5-41e2-bf3c-699d808d5faa\",\"type\":\"photo\",\"photoName\":\"20行,2列\",\"photoFormat\":\"png\",\"photoShowNum\":\"图2\",\"row\":20,\"class\":\"sign-img photo\"}],\"row\":20},{\"col\":3,\"eles\":[],\"row\":20},{\"col\":4,\"eles\":[],\"row\":20},{\"col\":5,\"eles\":[],\"row\":20},{\"col\":6,\"eles\":[],\"row\":20},{\"col\":7,\"eles\":[],\"row\":20},{\"col\":8,\"eles\":[],\"row\":20},{\"col\":0,\"eles\":[],\"row\":21},{\"col\":1,\"eles\":[],\"row\":21},{\"col\":2,\"eles\":[{\"date\":\"2024-03-28\",\"col\":2,\"src\":\"/File//2024-03//bf541a03-48a5-4dfc-bc41-713e36822938\",\"photoPath\":\"//2024-03//bf541a03-48a5-4dfc-bc41-713e36822938\",\"type\":\"photo\",\"photoName\":\"21行,2列\",\"photoFormat\":\"png\",\"photoShowNum\":\"图3\",\"row\":21,\"class\":\"sign-img photo\"}],\"row\":21},{\"col\":3,\"eles\":[],\"row\":21},{\"col\":4,\"eles\":[],\"row\":21},{\"col\":5,\"eles\":[],\"row\":21},{\"col\":6,\"eles\":[],\"row\":21},{\"col\":7,\"eles\":[],\"row\":21},{\"col\":8,\"eles\":[],\"row\":21}]}",
                                              "isParseSuccess": true,
                                              "node": "TEST2",
                                              "table3": "综合测试_6787972715",
                                              "table2": "2024-03-15",
                                              "table1": "A2",
                                              "model": "D1_1",
                                              "fileFormat": "xlsx",
                                              "status": "00"
                                            }
                                             */
                                            var obj = JSON.parse(fileStr);
                                        
                                            var fileName = obj.fileName;
                                            var fileFormat = obj.fileFormat;
                                            var filePath = obj.filePath;
                                            var fileSize = obj.fileSize;
                                            var status = obj.status;
                                            var isParseSuccess = obj.isParseSuccess;
                                            var parseFailedMsg = obj.parseFailedMsg || '';
                                            var model = obj.model;
                                            var node = obj.node;
                                            var nodeCode = me.GetMappingNodeCode({ nodeCode: obj.nodeCode });
                                            var table1 = obj.table1;
                                            var table2 = obj.table2;
                                            var table3 = obj.table3;
                                            var attachments = obj.attachments;
                                            var attachmentsStr = JSON.stringify(attachments);
                                            attachmentsStr = Things['Thing.Util.HandsonTable'].StrToClobSql({ str: attachmentsStr });
                                            var tableData = obj.tableData || '';
                                        
                                            var successTableId = -1;
                                            //查询model
                                            var modelSql = "select * from DATAPACKAGETREE where TEST_CODE = '" + model + "'";
                                            var modelRs = Things['Thing.DB.Oracle'].RunQuery({ sql: modelSql }).rows;
                                            if (modelRs.length == 0) {
                                                res.success = false;
                                                res.msg = model + "未关联到数据包系统中";
                                            } else {
                                                var modelId = modelRs[0].TREEID;
                                                //查询整星AIT节点
                                                var aitSql = "select * " +
                                                    "from DATAPACKAGETREE " +
                                                    "where PARENTID = " + modelId + " " +
                                                    "  and (NODENAME like '%整星AIT%' or NODENAME like '%整星ait%') ";
                                                var aitRs = Things['Thing.DB.Oracle'].RunQuery({ sql: aitSql }).rows;
                                                if (aitRs.length == 0) {
                                                    res.success = false;
                                                    res.msg = model + "关联的型号下未找到整星AIT节点";
                                                } else {
                                                    var aitId = aitRs[0].TREEID;
                                                    var nodeSql = "select * " +
                                                        "from DATAPACKAGETREE " +
                                                        "where PARENTID = " + aitId + " " +
                                                        "  and NODENAME = '" + nodeCode + "' ";
                                                    var nodeRs = Things['Thing.DB.Oracle'].RunQuery({ sql: nodeSql }).rows;
                                                    if (nodeRs.length == 0) {
                                                        res.success = false;
                                                        res.msg = model + "关联的型号下未找到" + nodeCode;
                                                    } else {
                                                        var reportThing = Things['Thing.Fn.QualityReport'];
                                                        var treeId = nodeRs[0].TREEID;
                                                        var reportPid = "T_" + treeId;
                                        
                                                        function createTableNode(treeId_, pid_, name_, type_) {
                                                            var resId;
                                                            //查询是否创建了
                                                            var querySql = "select * " +
                                                                "from QUALITY_REPORT " +
                                                                "where TREE_ID = " + treeId_ + " " +
                                                                " and TYPE='" + type_ + "' and PID = '" + pid_ + "' and NAME='" + name_ + "'";
                                                            var queryRs = Things['Thing.DB.Oracle'].RunQuery({ sql: querySql }).rows;
                                                            if (queryRs.length > 0) {
                                                                resId = queryRs[0].ID;
                                                            } else {
                                                                resId = reportThing.GetTreeNewId();
                                                                var sort_ = reportThing.GetSortByPid({
                                                                    pid: pid_
                                                                });
                                                                var level_ = 6;
                                                                if (type_ == 'report') {
                                                                    level_ = 6;
                                                                } else if (type_ == 'table') {
                                                                    level_ = 7;
                                                                } else if (type_ == 'table_1') {
                                                                    level_ = 8;
                                                                } else if (type_ == 'table_2') {
                                                                    level_ = 9;
                                                                } else if (type_ == 'table_3') {
                                                                    level_ = 10;
                                                                }
                                                                var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
                                                                var insertSql = "insert into QUALITY_REPORT (ID, PID, TREE_ID, NAME, TYPE, LEVEL_NUM, " +
                                                                    "                           CREATOR, CREATE_TIME, SORT, TABLE_STATUS, SECURITY,IS_ELECTRIC_TEST) " +
                                                                    "values (" + resId + ",'" + pid_ + "'," + treeId_ + ",'" + name_ + "','" + type_ + "'," + level_ + "," +
                                                                    "'admin','" + nowTime + "'," + sort_ + ",'edit',1,1)";
                                                                Things['Thing.DB.Oracle'].RunCommand({ sql: insertSql });
                                                            }
                                                            return resId;
                                                        }
                                        
                                                        function saveTableData(id_) {
                                                            if (status == 'AA') {
                                                                //作废状态
                                                                var updateStatusSql = "UPDATE QUALITY_REPORT SET IS_DELETE=1,NAME=NAME||'(作废)' where id=" + id_;
                                                                Things["Thing.DB.Oracle"].RunCommand({ sql: updateStatusSql });
                                                            }
                                                            if (isParseSuccess) {
                                                                successTableId = id_;
                                                                //查询当前表的状态 如果已经确认的话 则不更新
                                                                var statusRs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select TABLE_STATUS from QUALITY_REPORT where id=" + id_ }).rows;
                                                                var tableStatus = "";
                                                                if (statusRs.length > 0) {
                                                                    tableStatus = statusRs[0].TABLE_STATUS;
                                                                }
                                                                if (tableStatus !== 'sign') {
                                                                    var saveRs = reportThing.SaveTableData({
                                                                        id: id_,
                                                                        tableData: tableData,
                                                                        saveUser: 'admin'
                                                                    });
                                                                    if (saveRs.success) {
                                                                        //添加附件信息
                                                                        if (attachments.length > 0) {
                                                                            var attachmentsSql = "UPDATE QUALITY_REPORT SET ATTACHMENT = " + attachmentsStr + "  where id=" + id_;
                                                                            Things["Thing.DB.Oracle"].RunCommand({ sql: attachmentsSql });
                                                                        }
                                                                        res.success = true;
                                                                    } else {
                                                                        res.success = false;
                                                                        res.msg = 'SaveTableData-' + saveRs.msg;
                                                                    }
                                                                } else {
                                                                    res.success = false;
                                                                    res.msg = id_ + '已经确认，不可更新';
                                                                }
                                        
                                                            } else {
                                                                res.success = false;
                                                                res.msg = "解析确认表文件失败，原因：" + parseFailedMsg;
                                                            }
                                                        }
                                        
                                                        //查询是否创建了report 也就是一级节点
                                                        var reportId = createTableNode(treeId, reportPid, node, 'report');
                                        
                                                        if (table1 != '') {
                                                            var table1Id = createTableNode(treeId, reportId, table1, 'table');
                                                            if (table2 != '') {
                                                                var table2Id = createTableNode(treeId, table1Id, table2, 'table_1');
                                                                if (table3 != '') {
                                                                    var table3Id = createTableNode(treeId, table2Id, table3, 'table_2');
                                                                    saveTableData(table3Id);
                                                                } else {
                                                                    saveTableData(table2Id);
                                                                }
                                                            } else {
                                                                saveTableData(table1Id);
                                                            }
                                                        }
                                        
                                                    }
                                                }
                                            }
                                        
                                            function recordLog(tableMsg) {
                                                var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
                                                var is_parse_success = isParseSuccess ? 1 : 0;
                                                var newId = Things['Thing.DB.Oracle'].RunQuery({ sql: "select ELECTRIC_TEST_LOG_SEQ.nextval as id from dual" }).rows[0]['ID'];
                                                var insertLogSql = "insert into ELECTRIC_TEST_LOG (ID, TABLE_ID, STATUS, MODEL, NODE, " +
                                                    "                               NODE_CODE, TABLE1, TABLE2, TABLE3, FILE_NAME, " +
                                                    "                               FILE_FORMAT, FILE_PATH, FILE_SIZE, " +
                                                    "                               IS_PARSE_SUCCESS, PARSE_FAILED_MSG, ATTACHMENTS, " +
                                                    "                               CREATE_TIME, CREATOR, TABLE_MSG, UPDATE_TIME) " +
                                                    "values (" + newId + "," + successTableId + ",'" + status + "','" + model + "','" + node + "'," +
                                                    "'" + nodeCode + "','" + table1 + "','" + table2 + "','" + table3 + "','" + fileName + "'," +
                                                    "'" + fileFormat + "','" + filePath + "','" + fileSize + "'," +
                                                    "" + is_parse_success + ",'" + parseFailedMsg + "'," + attachmentsStr + "," +
                                                    "'" + nowTime + "','admin','" + tableMsg + "','" + nowTime + "')";
                                                Things['Thing.DB.Oracle'].RunCommand({ sql: insertLogSql });
                                                return newId;
                                            }
                                        
                                            if (res.success) {
                                                res.logId = recordLog("添加成功");
                                            } else {
                                                res.logId = recordLog(res.msg);
                                            }
                                        
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "SaveTestTable--失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="SyncAll">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    SyncAll
                                         * @description   同步全部 date 2024/3/29 15:00
                                         * @implementation    {Script}
                                         *
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            var postData = {
                                                type: "all",
                                                model: "null",
                                                nodeCode: "null"
                                            };
                                            var params = {
                                                url: Things['Thing.System'].GetFileHandleUrl() + "/electric/test/sync/create/file?type=" + postData.type + "&model=" + postData.model + "&nodeCode=" + postData.nodeCode,
                                            };
                                            var successNum = Resources["ContentLoaderFunctions"].PostText(params);
                                            successNum = parseInt(successNum);
                                            if (successNum > 0) {
                                                res.success = true;
                                                res.msg = "同步成功" + successNum + "个确表";
                                            } else {
                                                res.success = false;
                                                res.msg = "未发现待同步的确认表！";
                                            }
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "同步失败--失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="UpdateAllTestLog">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    UpdateAllTestLog
                                         * @description   更新全型号测试日志数据 date 2024/4/10 16:02
                                         * @implementation    {Script}
                                         *
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                            Things['Thing.Fn.ElectricTest'].InitTemporaryTable();
                                            Things['Thing.DB.Oracle'].RunCommand({sql: "delete from MODEL_TEST_EVENTS"});
                                            var params = {
                                                url: Things['Thing.System'].GetFileHandleUrl() + "/electric/test/sync/test/file",
                                                timeout:6000000
                                            };
                                            var dataText = Resources["ContentLoaderFunctions"].PostText(params);
                                            res.success = true;
                                            res.msg = "成功更新" + dataText + "条数据！";
                                        } catch (error) {
                                            res.success = false;
                                            res.msg = "UpdateTestLog-更新全型号测试日志数据-失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                    <ServiceImplementation
                     description=""
                     handlerName="Script"
                     name="UpdateTestLog">
                        <ConfigurationTables>
                            <ConfigurationTable
                             description=""
                             isMultiRow="false"
                             name="Script"
                             ordinal="0">
                                <DataShape>
                                    <FieldDefinitions>
                                        <FieldDefinition
                                         baseType="STRING"
                                         description="code"
                                         name="code"
                                         ordinal="0"></FieldDefinition>
                                    </FieldDefinitions>
                                </DataShape>
                                <Rows>
                                    <Row>
                                        <code>
                                        <![CDATA[
                                        /**
                                         * @definition    UpdateTestLog
                                         * @description   更新全型号测试日志数据 date 2024/4/10 16:02
                                         * @implementation    {Script}
                                         *
                                         * @param    {STRING}    dataStr    
                                         * @param    {NUMBER}    arrFirstIndex    
                                         *
                                         * @returns    {JSON}
                                         */
                                        var res = {};
                                        try {
                                        	var datas = JSON.parse(dataStr);
                                        
                                        	var insertAllSql = "insert all";
                                        	for (var i = 0; i < datas.length; i++) {
                                        		var d = datas[i];
                                        		var id = arrFirstIndex + i + 1;
                                        		var insertSql = " into MODEL_TEST_EVENTS(id";
                                        		var insertSqlValues = ") values(" + id;
                                        		for (var key in d) {
                                        			//headerAlias.put("卫星名称", "model_name");
                                        			//headerAlias.put("阶段名称", "stage_name");
                                        			//headerAlias.put("测试项目", "test_item");
                                        			//headerAlias.put("创建时间", "creation_time");
                                        			//headerAlias.put("备注", "remarks");
                                        			//headerAlias.put("编号", "code");
                                        			//headerAlias.put("发生时刻", "occurrence_time");
                                        			//headerAlias.put("异常现象", "exception_phenomenon");
                                        			//headerAlias.put("异常发生地点", "exception_location");
                                        			//headerAlias.put("异常产品名称", "exception_product_name");
                                        			//headerAlias.put("产品代号", "product_code");
                                        			//headerAlias.put("批次号/版本号", "batch_or_version");
                                        			//headerAlias.put("责任单位", "responsible_unit");
                                        			//headerAlias.put("现象描述", "phenomenon_description");
                                        			//headerAlias.put("问题分类", "problem_category");
                                        			//headerAlias.put("初步定位", "initial_position");
                                        			//headerAlias.put("处理措施", "handling_measures");
                                        			//headerAlias.put("处置确认-509所十八室", "confirmation_509_18");
                                        			//headerAlias.put("处置确认-812所测试中心", "confirmation_812_test");
                                        			//headerAlias.put("闭环情况", "closure_status");
                                        			//headerAlias.put("闭环情况描述", "closure_description");
                                        			//headerAlias.put("闭环时间", "closure_time");
                                        			//headerAlias.put("闭环情况-509所质量师", "closure_509_quality");
                                        			//headerAlias.put("闭环情况-812所质量师", "closure_812_quality");
                                        			if (key == 'model_name' || key == 'stage_name' || key == 'test_item' || key == 'creation_time' ||
                                        				key == 'creation_time' || key == 'remarks' || key == 'code' ||
                                        				key == 'occurrence_time' || key == 'exception_phenomenon' ||
                                        				key == 'exception_location' || key == 'exception_product_name' || key == 'product_code' ||
                                        				key == 'batch_or_version' || key == 'responsible_unit' || key == 'phenomenon_description' ||
                                        				key == 'problem_category' || key == 'initial_position' || key == 'handling_measures' ||
                                        				key == 'confirmation_509_18' || key == 'confirmation_812_test' || key == 'closure_status' ||
                                        				key == 'closure_description' || key == 'closure_time' || key == 'closure_509_quality' ||
                                        				key == 'closure_812_quality') {
                                        				insertSql += "," + key;
                                        				insertSqlValues += ",'" + d[key] + "'";
                                        			}
                                        		}
                                        		insertSql += insertSqlValues + ")";
                                        		insertAllSql += insertSql;
                                        	}
                                        	insertAllSql += " SELECT * FROM dual"
                                        	Things['Thing.DB.Oracle'].RunCommand({ sql: insertAllSql });
                                        	res.success = true;
                                        	res.data = datas.length;
                                        } catch (error) {
                                        	res.success = false;
                                        	res.msg = "UpdateTestLog-更新一条测试日志数据-失败，原因：" + error;
                                        }
                                        result = res;
                                        ]]>
                                        </code>
                                    </Row>
                                </Rows>
                            </ConfigurationTable>
                        </ConfigurationTables>
                    </ServiceImplementation>
                </ServiceImplementations>
                <Subscriptions>
                    <Subscription
                     enabled="true"
                     eventName="ScheduledEvent"
                     source="Thing.Timer.ElectricTest"
                     sourceProperty=""
                     sourceType="Thing">
                        <ServiceImplementation
                         description=""
                         handlerName="Script"
                         name="Type.Thing:Entity.Thing.Timer.ElectricTest:Event.ScheduledEvent">
                            <ConfigurationTables>
                                <ConfigurationTable
                                 description=""
                                 isMultiRow="false"
                                 name="Script"
                                 ordinal="0">
                                    <DataShape>
                                        <FieldDefinitions>
                                            <FieldDefinition
                                             baseType="STRING"
                                             description="code"
                                             name="code"
                                             ordinal="0"></FieldDefinition>
                                        </FieldDefinitions>
                                    </DataShape>
                                    <Rows>
                                        <Row>
                                            <code>
                                            <![CDATA[
                                            var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
                                                                                                                                                                                                                            logger.error(nowTime+" 开始自动同步电测试系统数据-----------------------");
                                                                                                                                                                                                                            me.UpdateAllTestLog();
                                                                                                                                                                                                                            var res = me.SyncAll();
                                                                                                                                                                                                                            logger.error(nowTime+" 同步电测试系统数据-----------------------结束："+JSON.stringify(res));
                                            ]]>
                                            </code>
                                        </Row>
                                    </Rows>
                                </ConfigurationTable>
                            </ConfigurationTables>
                        </ServiceImplementation>
                    </Subscription>
                </Subscriptions>
            </ThingShape>
            <PropertyBindings></PropertyBindings>
            <RemotePropertyBindings></RemotePropertyBindings>
            <RemoteServiceBindings></RemoteServiceBindings>
            <RemoteEventBindings></RemoteEventBindings>
            <AlertConfigurations>
                <AlertDefinitions
                 name="syncDirectory"></AlertDefinitions>
                <AlertDefinitions
                 name="testFileName"></AlertDefinitions>
            </AlertConfigurations>
            <ImplementedShapes></ImplementedShapes>
            <ThingProperties>
                <testFileName>
                    <Value>
                    <![CDATA[
                    11#$C1#$全型号测试日志表
                    ]]>
                    </Value>
                    <Timestamp>2024-04-24T10:39:44.474+08:00</Timestamp>
                    <Quality>GOOD</Quality>
                </testFileName>
                <syncDirectory>
                    <Value>
                    <![CDATA[
                    F:\ThingworxDataCollect\09-测试中心质量表
                    ]]>
                    </Value>
                    <Timestamp>2024-04-18T09:38:37.531+08:00</Timestamp>
                    <Quality>GOOD</Quality>
                </syncDirectory>
            </ThingProperties>
        </Thing>
    </Things>
</Entities>
