package com.cirpoint.annotation;

import java.lang.annotation.*;

/**
 * 性能监控注解
 * 用于标记需要进行性能统计的方法
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PerformanceMonitor {
    /**
     * 监控描述
     */
    String description() default "";
    
    /**
     * 是否记录参数
     */
    boolean logParams() default true;
    
    /**
     * 是否记录返回值
     */
    boolean logReturn() default false;
}
